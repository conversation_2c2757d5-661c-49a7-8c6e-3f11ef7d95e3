#!/usr/bin/env python3
"""
依赖注入系统测试脚本
Dependency Injection System Test Script

此脚本用于测试新的依赖注入系统是否正常工作。
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_config_loading():
    """测试配置加载"""
    print("🔧 测试配置加载...")
    
    try:
        from src.config.settings import (
            database_settings,
            minio_settings,
            llm_settings,
            dify_settings,
            video_api_settings
        )
        
        print(f"✅ 数据库配置: {database_settings.DATABASE_URL[:20]}...")
        print(f"✅ MinIO配置: {minio_settings.MINIO_ENDPOINT}")
        print(f"✅ 视频API配置: {video_api_settings.BUCKET_NAME}")
        print(f"✅ 配置加载成功")
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


async def test_service_creation():
    """测试服务创建"""
    print("\n🏭 测试服务创建...")
    
    try:
        from src.core.dependencies import (
            get_minio_service,
            get_dify_service,
            get_llm_service
        )
        
        # 测试MinIO服务
        minio_service = get_minio_service()
        print(f"✅ MinIO服务创建成功: {type(minio_service).__name__}")
        
        # 测试Dify服务
        dify_service = get_dify_service()
        print(f"✅ Dify服务创建成功: {type(dify_service).__name__}")
        
        # 测试LLM服务
        llm_service = get_llm_service()
        print(f"✅ LLM服务创建成功: {type(llm_service).__name__}")
        
        print(f"✅ 服务创建成功")
        return True
        
    except Exception as e:
        print(f"❌ 服务创建失败: {e}")
        return False


async def test_service_singleton():
    """测试服务单例模式"""
    print("\n🔄 测试服务单例模式...")
    
    try:
        from src.core.dependencies import get_minio_service
        
        service1 = get_minio_service()
        service2 = get_minio_service()
        
        if service1 is service2:
            print("✅ 服务单例模式正常工作")
            return True
        else:
            print("❌ 服务单例模式失败")
            return False
            
    except Exception as e:
        print(f"❌ 服务单例测试失败: {e}")
        return False


async def test_service_health():
    """测试服务健康检查"""
    print("\n🏥 测试服务健康检查...")
    
    try:
        from src.core.dependencies import check_services_health
        
        health_status = await check_services_health()
        print(f"✅ 服务健康检查完成:")
        
        for service, status in health_status.items():
            if isinstance(status, dict) and status.get("status") == "error":
                print(f"  ⚠️  {service}: {status.get('error', 'Unknown error')}")
            else:
                print(f"  ✅ {service}: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务健康检查失败: {e}")
        return False


async def test_api_imports():
    """测试API模块导入"""
    print("\n📡 测试API模块导入...")
    
    try:
        from src.video_rag.api import video_router
        print(f"✅ 视频API路由导入成功")
        
        # 检查路由数量
        route_count = len(video_router.routes)
        print(f"✅ 发现 {route_count} 个API路由")
        
        return True
        
    except Exception as e:
        print(f"❌ API模块导入失败: {e}")
        return False


async def test_main_app():
    """测试主应用"""
    print("\n🚀 测试主应用...")
    
    try:
        from src.main import app
        print(f"✅ 主应用创建成功")
        
        # 检查中间件
        middleware_count = len(app.user_middleware)
        print(f"✅ 发现 {middleware_count} 个中间件")
        
        return True
        
    except Exception as e:
        print(f"❌ 主应用测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 开始依赖注入系统测试")
    print("=" * 50)
    
    tests = [
        ("配置加载", test_config_loading),
        ("服务创建", test_service_creation),
        ("服务单例", test_service_singleton),
        ("服务健康", test_service_health),
        ("API导入", test_api_imports),
        ("主应用", test_main_app),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！依赖注入系统工作正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查配置和依赖。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
