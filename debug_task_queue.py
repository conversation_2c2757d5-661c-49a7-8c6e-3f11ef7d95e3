#!/usr/bin/env python3
"""
调试任务队列注入问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_task_queue():
    """调试任务队列注入"""
    print("🔍 调试任务队列注入...")
    
    from src.core.dependencies import get_document_processor, get_document_task_queue
    
    # 获取单例实例
    processor = get_document_processor()
    task_queue = get_document_task_queue()
    
    print(f"处理器实例ID: {id(processor)}")
    print(f"任务队列实例ID: {id(task_queue)}")
    print(f"任务队列处理器ID: {id(task_queue.processor)}")
    print(f"处理器相同: {task_queue.processor is processor}")
    
    # 再次获取看看
    processor2 = get_document_processor()
    task_queue2 = get_document_task_queue()
    
    print(f"\n第二次获取:")
    print(f"处理器实例ID: {id(processor2)}")
    print(f"任务队列实例ID: {id(task_queue2)}")
    print(f"任务队列处理器ID: {id(task_queue2.processor)}")
    print(f"处理器相同: {task_queue2.processor is processor2}")
    print(f"任务队列相同: {task_queue2 is task_queue}")

if __name__ == "__main__":
    debug_task_queue()
