<template>
  <div class="app-container">
    <el-row :gutter="10" class="mb8 justify-end">
      <el-col :span="10">  <!-- Use half the space for each button -->
        <el-button type="info" plain icon="Upload" @click="handleImport">导入</el-button>
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" style="margin-left: 20px;">批量删除</el-button>  <!-- Added margin for better separation -->
      </el-col>
    </el-row>


    <div v-if="reconscaseList.length > 0">
      <el-table border v-loading="loading" :data="reconscaseList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55px" align="left" />
        <el-table-column label="序号" type="index" width="130px" :show-overflow-tooltip="true" />
        <el-table-column label="文本标题" align="left" prop="name" />
        <el-table-column label="字符数" align="left" prop="chunk_count" />
        <el-table-column label="导入时间" align="left" prop="process_begin_at">
          <template #default="scope">
            {{ parseTime(scope.row.process_begin_at) }}
          </template>
        </el-table-column>
        <el-table-column label="文档处理状态" align="left" prop="run" />
        <el-table-column label="操作" align="left">
          <template #default="scope">
            <el-button link type="success" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <el-dialog :title="title" v-model="open" width="900px" append-to-body>
      <el-form ref="reconscaseRef" :model="form" :rules="rules" label-width="110px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属区域:" prop="region">
              <span>{{ form.region }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案件编号:" prop="caseNumber">
              <span>{{ form.caseNumber }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="案件流水号:" prop="caseSerialNumber">
              <span>{{ form.caseSerialNumber }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人:" prop="applicant">
              <span>{{ form.applicant }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="被申请人:" prop="respondent">
              <span>{{ form.respondent }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请日期:" prop="dateApplication">
              <span>{{ form.dateApplication }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请方式:" prop="howApply">
              <span>{{ form.howApply }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="复议机关:" prop="reconsiderationOrgans">
              <span>{{ form.reconsiderationOrgans }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="承办人:" prop="undertaker">
              <span>{{ form.undertaker }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受理承办人:" prop="acceptanceUndertaker">
              <span>{{ form.acceptanceUndertaker }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="审理承办人:" prop="undertakerTrial">
              <span>{{ form.undertakerTrial }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="受理类型:" prop="typeAcceptance">
              <span>{{ form.typeAcceptance }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结案日期:" prop="caseClosureDate">
              <span>{{ form.caseClosureDate }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="复议事项:" prop="reconsiderationMatters">
              <span>{{ form.reconsiderationMatters }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行政行为名称:" prop="xwName">
              <span>{{ form.xwName }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="行政行为文号：" prop="xwNumber">
              <span>{{ form.xwNumber }}</span>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="附件地址">
          <file-upload v-if="isDow" v-model="form.fileUrl" />
          <span v-else>{{ form.form }}</span>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer" v-if="isDow">
          <el-button :loading="buttonLoading" type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog :title="upload.title" v-model="upload.open" width="600px" append-to-body>
      <file-upload :limit="50" v-model="form.fileUrl" @success="handleUploadSuccess" />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ReconsCase">
import { deletefile, getfiles, uploadFileKnow } from "@/api/data/index.js";
import { ref, reactive, getCurrentInstance } from "vue";

const { proxy } = getCurrentInstance();
const reconscaseList = ref([]);
const open = ref(false);
const buttonLoading = ref(false);
const loading = ref(false);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const isDow = ref(false);

const data = reactive({
  form: {},
  queryParams: {
    page: 1,
    page_size: 20,
  },
  rules: {},
});

const { queryParams, form, rules } = toRefs(data);

function getList() {
  loading.value = true;
  getfiles(queryParams.value).then((response) => {
    reconscaseList.value = response.items;
    total.value = response.total;
    loading.value = false;
  });
}

function cancel() {
  open.value = false;
  reset();
}

function reset() {
  form.value = {
    id: null,
    region: null,
    caseNumber: null,
    caseSerialNumber: null,
    applicant: null,
    respondent: null,
    dateApplication: null,
    howApply: null,
    reconsiderationOrgans: null,
    undertaker: null,
    acceptanceUndertaker: null,
    undertakerTrial: null,
    caseStatus: "0",
    typeAcceptance: null,
    caseClosureType: null,
    caseClosureDate: null,
    reconsiderationMatters: null,
    managementType: null,
    fileUrl: null,
    xwName: null,
    xwNumber: null,
  };
  proxy.resetForm("reconscaseRef");
}

function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

// handleUpdate function removed completely

function submitForm() {
  proxy.$refs["reconscaseRef"].validate((valid) => {
    if (valid) {
      buttonLoading.value = true;
      if (form.value.id != null) {
        updateReconsCase(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          })
          .finally(() => {
            buttonLoading.value = false;
          });
      } else {
        addReconsCase(form.value)
          .then((response) => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          })
          .finally(() => {
            buttonLoading.value = false;
          });
      }
    }
  });
}

function handleDelete(row) {
  const idsVal = row.id || ids.value;
  proxy.$modal
    .confirm('是否确认删除ID为"' + idsVal + '"的知识库？')
    .then(function () {
      loading.value = true;
      return deletefile(idsVal);
    })
    .then(() => {
      loading.value = true;
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .finally(() => {
      loading.value = false;
    });
}


const upload = reactive({
  open: false,
  title: "导入信息",
  isUploading: false,
  headers: { Authorization: "Bearer " },
  url: import.meta.env.VITE_APP_BASE_API + "/api/v1/documents/upload",
});

function handleImport() {
  upload.title = "用户导入";
  upload.open = true;
}

const fileList = ref([]);

function submitFileForm() {
  if (fileList.value.length === 0) {
    proxy.$modal.msgError("请选取文件后再上传");
    return;
  }

  const formData = new FormData();
  const files = fileList.value.map((file) => file.raw);
  formData.append("files", files);
  loadingUpload.value = true;

  uploadFileKnow(formData)
    .then((res) => {
      proxy.$modal.msgSuccess("上传成功");
      fileList.value = [];
      loadingUpload.value = false;
    })
    .catch((err) => {
      proxy.$modal.msgError("上传失败");
      loadingUpload.value = false;
    });
}

function handleChange(file, fileListParam) {
  fileList.value = fileListParam;
}

function handleRemove(file, fileListParam) {
  fileList.value = fileListParam;
}

const handleUploadSuccess = async (res) => {
  if (res.code === 200) {
    proxy.$modal.msgSuccess("上传成功");
    upload.open = false;
    await getList();
  }
};

getList();
</script>

<style>
.flex {
  display: flex;
}
</style>