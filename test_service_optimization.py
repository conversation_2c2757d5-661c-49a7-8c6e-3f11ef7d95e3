#!/usr/bin/env python3
"""
服务实例化优化测试
Service Instantiation Optimization Test

验证服务实例化优化是否正确工作，确保：
1. 单例模式正确工作
2. 依赖注入正确传递服务实例
3. 没有重复创建服务实例
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_singleton_pattern():
    """测试单例模式"""
    print("\n🔄 测试单例模式...")
    
    try:
        from src.core.dependencies import (
            get_minio_service,
            get_dify_service,
            get_llm_service,
            get_document_processor,
            get_document_task_queue
        )
        
        # 测试服务单例
        minio1 = get_minio_service()
        minio2 = get_minio_service()
        assert minio1 is minio2, "MinIO服务单例失败"
        print("✅ MinIO服务单例正常")
        
        dify1 = get_dify_service()
        dify2 = get_dify_service()
        assert dify1 is dify2, "Dify服务单例失败"
        print("✅ Dify服务单例正常")
        
        llm1 = get_llm_service()
        llm2 = get_llm_service()
        assert llm1 is llm2, "LLM服务单例失败"
        print("✅ LLM服务单例正常")
        
        # 测试文档处理器单例
        processor1 = get_document_processor()
        processor2 = get_document_processor()
        assert processor1 is processor2, "文档处理器单例失败"
        print("✅ 文档处理器单例正常")
        
        # 测试任务队列单例
        queue1 = get_document_task_queue()
        queue2 = get_document_task_queue()
        assert queue1 is queue2, "任务队列单例失败"
        print("✅ 任务队列单例正常")
        
        return True
        
    except Exception as e:
        print(f"❌ 单例模式测试失败: {e}")
        return False


async def test_service_injection():
    """测试服务注入"""
    print("\n💉 测试服务注入...")
    
    try:
        from src.core.dependencies import (
            get_minio_service,
            get_dify_service,
            get_alibaba_cloud_service,
            get_document_processor
        )
        
        # 获取单例服务
        minio_service = get_minio_service()
        dify_service = get_dify_service()
        alibaba_service = get_alibaba_cloud_service()
        
        # 获取文档处理器
        processor = get_document_processor()
        
        # 验证文档处理器使用的是相同的服务实例
        assert processor.minio_service is minio_service, "文档处理器MinIO服务注入失败"
        print("✅ 文档处理器MinIO服务注入正确")
        
        assert processor.dify_service is dify_service, "文档处理器Dify服务注入失败"
        print("✅ 文档处理器Dify服务注入正确")
        
        assert processor.alibaba_service is alibaba_service, "文档处理器阿里云服务注入失败"
        print("✅ 文档处理器阿里云服务注入正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 服务注入测试失败: {e}")
        return False


async def test_task_queue_injection():
    """测试任务队列注入"""
    print("\n🔗 测试任务队列注入...")
    
    try:
        from src.core.dependencies import get_document_processor, get_document_task_queue
        
        # 获取单例实例
        processor = get_document_processor()
        task_queue = get_document_task_queue()
        
        # 验证任务队列使用的是相同的处理器实例
        assert task_queue.processor is processor, "任务队列处理器注入失败"
        print("✅ 任务队列处理器注入正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务队列注入测试失败: {e}")
        return False


async def test_global_instance():
    """测试全局实例"""
    print("\n🌍 测试全局实例...")

    try:
        from src.document_rag.document_processor import document_task_queue, get_global_document_task_queue
        from src.core.dependencies import get_document_task_queue

        # 获取依赖注入的实例
        di_queue = get_document_task_queue()

        # 获取全局实例的真实对象
        global_queue = get_global_document_task_queue()

        # 验证全局实例使用的是相同的依赖注入实例
        assert global_queue is di_queue, "全局任务队列实例不一致"
        print("✅ 全局任务队列实例正确")

        # 验证代理对象能正确访问属性
        assert hasattr(document_task_queue, 'processor'), "代理对象无法访问processor属性"
        print("✅ 代理对象工作正常")

        return True

    except Exception as e:
        print(f"❌ 全局实例测试失败: {e}")
        return False


async def test_deprecation_warnings():
    """测试废弃警告"""
    print("\n⚠️ 测试废弃警告...")
    
    try:
        import warnings
        
        # 捕获警告
        with warnings.catch_warnings(record=True) as w:
            warnings.simplefilter("always")
            
            # 测试废弃的便捷函数
            from src.core.dependencies import create_minio_service
            create_minio_service()
            
            # 检查是否有废弃警告
            deprecation_warnings = [warning for warning in w if issubclass(warning.category, DeprecationWarning)]
            assert len(deprecation_warnings) > 0, "没有发出废弃警告"
            print("✅ 废弃警告正常工作")
        
        return True
        
    except Exception as e:
        print(f"❌ 废弃警告测试失败: {e}")
        return False


async def test_memory_efficiency():
    """测试内存效率"""
    print("\n💾 测试内存效率...")
    
    try:
        from src.core.dependencies import get_minio_service
        
        # 多次获取服务，应该是同一个实例
        services = [get_minio_service() for _ in range(10)]
        
        # 验证所有实例都是同一个对象
        first_service = services[0]
        for i, service in enumerate(services[1:], 1):
            assert service is first_service, f"第{i+1}个实例不是单例"
        
        print("✅ 内存效率测试通过 - 所有实例都是单例")
        return True
        
    except Exception as e:
        print(f"❌ 内存效率测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 开始服务实例化优化测试")
    print("=" * 60)
    
    tests = [
        ("单例模式", test_singleton_pattern),
        ("服务注入", test_service_injection),
        ("任务队列注入", test_task_queue_injection),
        ("全局实例", test_global_instance),
        ("废弃警告", test_deprecation_warnings),
        ("内存效率", test_memory_efficiency),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            if result:
                passed += 1
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！服务实例化优化工作正常。")
        print("\n📈 优化效果:")
        print("  ✅ 服务实例使用单例模式，避免重复创建")
        print("  ✅ 依赖注入正确传递服务实例")
        print("  ✅ 内存使用更加高效")
        print("  ✅ 代码结构更加清晰")
        return 0
    else:
        print("⚠️  部分测试失败，请检查服务实例化配置。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
