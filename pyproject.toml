[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "video-rag"
version = "0.1.0"
description = "视频RAG召回系统 - 基于多模态大模型的智能视频检索系统"
readme = "READEMD.md"
license = {text = "MIT"}
authors = [
    {name = "Video RAG Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Video RAG Team", email = "<EMAIL>"}
]
keywords = ["video", "rag", "ai", "multimodal", "retrieval", "fastapi"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Multimedia :: Video",
]
requires-python = ">=3.10"
dependencies = [
    "fastapi>=0.104.0,<1.0.0",
    "uvicorn[standard]>=0.24.0,<1.0.0",
    "tortoise-orm>=0.20.0,<1.0.0",
    "aerich>=0.7.2,<1.0.0",
    "aiomysql>=0.2.0,<1.0.0",
    "pymysql>=1.1.0,<2.0.0",
    "pydantic>=2.5.0,<3.0.0",
    "pydantic-settings>=2.1.0,<3.0.0",
    "langchain-openai>=0.1.0,<1.0.0",
    "langchain-core>=0.2.0,<1.0.0",
    "minio>=7.2.0,<8.0.0",
    "alibabacloud-docmind-api20220711>=1.0.0,<2.0.0",
    "alibabacloud-tea-openapi>=0.3.0,<1.0.0",
    "alibabacloud-tea-util>=0.3.0,<1.0.0",
    "alibabacloud-credentials>=0.3.0,<1.0.0",
    "httpx>=0.25.0,<1.0.0",
    "aiohttp>=3.9.0,<4.0.0",
    "python-multipart>=0.0.6,<1.0.0",
    "python-dotenv>=1.0.0,<2.0.0",
    "typing-extensions>=4.8.0,<5.0.0",
    "orjson>=3.9.0,<4.0.0",
    "structlog>=23.2.0,<24.0.0",
    "cryptography>=41.0.0,<42.0.0",
    "python-dateutil>=2.8.0,<3.0.0",
    "psutil>=5.9.0,<6.0.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
    "mypy>=1.5.0",
    "pre-commit>=3.4.0",
    "httpx>=0.25.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "httpx>=0.25.0",
]
docs = [
    "mkdocs>=1.5.0",
    "mkdocs-material>=9.2.0",
    "mkdocstrings[python]>=0.23.0",
]

[project.urls]
Homepage = "https://github.com/your-org/video-rag"
Documentation = "https://video-rag.readthedocs.io/"
Repository = "https://github.com/your-org/video-rag.git"
"Bug Tracker" = "https://github.com/your-org/video-rag/issues"

[project.scripts]
video-rag = "src.main:main"

[tool.setuptools.packages.find]
where = ["src"]
include = ["*"]

[tool.setuptools.package-data]
"*" = ["*.txt", "*.md", "*.yml", "*.yaml", "*.json"]

# ===== Development Tools Configuration =====

[tool.black]
line-length = 88
target-version = ['py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
  | migrations
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["src"]
skip_glob = ["migrations/*"]

[tool.mypy]
python_version = "3.10"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true
exclude = [
    "migrations/",
    "build/",
    "dist/",
]

[[tool.mypy.overrides]]
module = [
    "minio.*",
    "alibabacloud_*",
    "tortoise.*",
    "aerich.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
asyncio_mode = "auto"
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
]

[tool.coverage.run]
source = ["src"]
omit = [
    "*/migrations/*",
    "*/tests/*",
    "*/venv/*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

# ===== Database Migration Configuration =====

[tool.aerich]
tortoise_orm = "src.core.config.TORTOISE_CONFIG"
location = "./migrations"
src_folder = "./."
