#!/usr/bin/env python3
"""
测试修复后的文档上传功能
Test Fixed Document Upload Functionality
"""

import asyncio
import httpx
import os
import sys

# 测试文件路径
TEST_FILE_PATH = "src/docs/测试文档/G87.pdf"
API_BASE_URL = "http://localhost:8000"


async def test_document_upload():
    """测试文档上传"""
    print("🚀 开始测试文档上传...")
    
    if not os.path.exists(TEST_FILE_PATH):
        print(f"❌ 测试文件不存在: {TEST_FILE_PATH}")
        return False
    
    try:
        async with httpx.AsyncClient(timeout=60.0) as client:
            # 准备文件上传
            with open(TEST_FILE_PATH, "rb") as f:
                files = {"files": ("G87.pdf", f, "application/pdf")}
                data = {"batch_name": "测试批次"}
                
                print(f"📤 上传文件: {TEST_FILE_PATH}")
                response = await client.post(
                    f"{API_BASE_URL}/api/v1/documents/upload",
                    files=files,
                    data=data
                )
                
                print(f"📊 响应状态码: {response.status_code}")
                print(f"📊 响应内容: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 文档上传成功!")
                    print(f"📋 批次ID: {result.get('batch_id')}")
                    print(f"📋 上传文件数: {len(result.get('uploaded_files', []))}")
                    
                    # 获取第一个上传的文件的task_id
                    uploaded_files = result.get('uploaded_files', [])
                    if uploaded_files:
                        task_id = uploaded_files[0].get('task_id')
                        print(f"📋 任务ID: {task_id}")
                        
                        # 等待一段时间让Redis任务开始处理
                        print("⏳ 等待5秒让任务开始处理...")
                        await asyncio.sleep(5)
                        
                        # 查询任务状态
                        await check_task_status(client, task_id)
                        
                        return True
                    else:
                        print("❌ 没有获取到上传文件信息")
                        return False
                else:
                    print(f"❌ 文档上传失败: {response.text}")
                    return False
                    
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


async def check_task_status(client: httpx.AsyncClient, task_id: str):
    """检查任务状态"""
    print(f"\n🔍 检查任务状态: {task_id}")
    
    try:
        response = await client.get(f"{API_BASE_URL}/api/v1/documents/{task_id}/status")
        
        if response.status_code == 200:
            status_data = response.json()
            print(f"📊 任务状态: {status_data}")
        else:
            print(f"❌ 获取任务状态失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 检查任务状态异常: {e}")


async def test_redis_queue_stats():
    """测试Redis队列统计"""
    print("\n📊 检查Redis队列统计...")
    
    try:
        from src.core.dependencies import get_redis_queue_service
        redis_queue = get_redis_queue_service()
        
        stats = await redis_queue.get_queue_stats()
        print(f"📊 队列统计: {stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ 获取队列统计失败: {e}")
        return False


async def main():
    """主函数"""
    print("=" * 60)
    print("🧪 测试修复后的文档上传和Redis任务队列")
    print("=" * 60)
    
    # 测试Redis队列统计
    await test_redis_queue_stats()
    
    # 测试文档上传
    success = await test_document_upload()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试完成！请查看服务器日志了解详细处理过程。")
    else:
        print("💥 测试失败！请检查错误信息。")
    print("=" * 60)


if __name__ == "__main__":
    asyncio.run(main())
