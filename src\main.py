from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
import asyncio
from src.core.database import init_db, close_db
from src.config.settings import app_settings

# 视频处理api
from src.video_rag.api import video_router

# 文档处理api
from src.document_rag.api import document_rag_router



# 定义一个异步上下文管理器来管理应用生命周期中的数据库连接
@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    异步上下文管理器用于管理应用生命周期中的数据库连接。
    在应用生命周期中，如果有异步任务或者其他异步操作，则必须使用该管理器来处理它们。
    """
    print("初始化数据库连接...")
    await init_db()

    # 启动Redis任务工作进程
    from src.core.dependencies import get_redis_queue_service
    from src.services.redis_queue_service import RedisTaskWorker
    from src.document_rag.document_processor import process_document_task

    print("启动Redis任务工作进程...")
    redis_queue = get_redis_queue_service()
    worker = RedisTaskWorker(redis_queue, worker_id="main-worker")

    # 在后台启动Redis任务工作进程
    task = asyncio.create_task(worker.start(process_document_task))

    try:
        yield
    finally:
        print("停止Redis任务工作进程...")
        worker.stop()
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

        print("关闭数据库连接...")
        await close_db()
        
app = FastAPI(lifespan=lifespan)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=app_settings.ALLOWED_ORIGINS,
    allow_credentials=True,
    allow_methods=app_settings.ALLOWED_METHODS,
    allow_headers=app_settings.ALLOWED_HEADERS,
)

@app.get("/")
async def read_root():
    return {"message": "Hello, World!"}

# 引入视频处理api
app.include_router(video_router, prefix="/api/video")

# 引入文档处理api
app.include_router(document_rag_router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        app,
        host=app_settings.HOST,
        port=app_settings.PORT,
        reload=app_settings.RELOAD
    )