#!/usr/bin/env python3
"""
调试构造函数调用
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_constructor():
    """调试构造函数调用"""
    print("🔍 调试构造函数调用...")
    
    from src.core.dependencies import clear_service_cache, get_document_processor
    from src.document_rag.document_processor import DocumentTaskQueue
    
    # 清理缓存
    clear_service_cache()
    
    # 获取处理器
    print("获取处理器...")
    processor = get_document_processor()
    print(f"处理器实例ID: {id(processor)}")
    
    # 直接创建任务队列
    print("直接创建任务队列...")
    task_queue = DocumentTaskQueue(processor=processor)
    print(f"任务队列实例ID: {id(task_queue)}")
    print(f"任务队列处理器ID: {id(task_queue.processor)}")
    print(f"处理器相同: {task_queue.processor is processor}")

if __name__ == "__main__":
    debug_constructor()
