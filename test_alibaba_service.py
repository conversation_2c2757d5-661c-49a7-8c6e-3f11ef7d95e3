#!/usr/bin/env python3
"""
测试阿里云文档解析服务
Test Alibaba Cloud Document Parsing Service
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.document_rag.ali_service import AlibabaCloudService
from src.config.settings import alibaba_cloud_settings


async def test_alibaba_service():
    """测试阿里云服务"""
    try:
        service = AlibabaCloudService(alibaba_cloud_settings)
        
        print("✅ 阿里云服务初始化成功")
        
        # 测试提交一个简单的文档（如果有的话）
        # 这里只是测试服务是否能正常初始化和调用
        print("🔧 阿里云服务测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 阿里云服务测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("开始测试阿里云文档解析服务...")
    
    success = await test_alibaba_service()
    
    if success:
        print("\n🎉 阿里云服务测试通过！")
    else:
        print("\n💥 阿里云服务测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
