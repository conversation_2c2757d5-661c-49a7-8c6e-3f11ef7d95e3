#!/usr/bin/env python3
"""
调试单例模式问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_singleton():
    """调试单例模式"""
    print("🔍 调试单例模式...")
    
    from src.core.dependencies import (
        get_minio_service,
        get_dify_service,
        get_alibaba_cloud_service,
        get_document_processor
    )
    
    # 测试服务单例
    print("\n1. 测试服务单例:")
    minio1 = get_minio_service()
    minio2 = get_minio_service()
    print(f"MinIO服务: {id(minio1)} vs {id(minio2)} - 相同: {minio1 is minio2}")
    
    dify1 = get_dify_service()
    dify2 = get_dify_service()
    print(f"Dify服务: {id(dify1)} vs {id(dify2)} - 相同: {dify1 is dify2}")
    
    alibaba1 = get_alibaba_cloud_service()
    alibaba2 = get_alibaba_cloud_service()
    print(f"阿里云服务: {id(alibaba1)} vs {id(alibaba2)} - 相同: {alibaba1 is alibaba2}")
    
    # 测试文档处理器单例
    print("\n2. 测试文档处理器单例:")
    processor1 = get_document_processor()
    processor2 = get_document_processor()
    print(f"文档处理器: {id(processor1)} vs {id(processor2)} - 相同: {processor1 is processor2}")
    
    # 检查文档处理器内部的服务实例
    print("\n3. 检查文档处理器内部服务:")
    print(f"处理器1 MinIO: {id(processor1.minio_service)}")
    print(f"处理器2 MinIO: {id(processor2.minio_service)}")
    print(f"独立 MinIO: {id(minio1)}")
    print(f"处理器MinIO相同: {processor1.minio_service is minio1}")
    
    # 检查缓存信息
    print("\n4. 检查缓存信息:")
    print(f"get_minio_service 缓存信息: {get_minio_service.cache_info()}")
    print(f"get_dify_service 缓存信息: {get_dify_service.cache_info()}")
    print(f"get_alibaba_cloud_service 缓存信息: {get_alibaba_cloud_service.cache_info()}")
    print("文档处理器使用全局单例模式（非LRU缓存）")

if __name__ == "__main__":
    debug_singleton()
