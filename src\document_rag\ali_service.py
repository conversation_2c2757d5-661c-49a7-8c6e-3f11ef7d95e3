# 阿里云文档解析服务
import asyncio
from typing import Dict, Any, Optional
from src.config.settings import AlibabaCloudSettings

# 阿里云文档解析包
from alibabacloud_docmind_api20220711.client import Client as docmind_api20220711Client
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_docmind_api20220711 import models as docmind_api20220711_models
from alibabacloud_tea_util.client import Client as UtilClient
from alibabacloud_tea_util import models as util_models
from alibabacloud_credentials.client import Client as CredClient


class AlibabaCloudService:
    def __init__(self, settings: AlibabaCloudSettings):
        """
        初始化阿里云文档解析服务

        Args:
            settings: 阿里云配置实例
        """
        self.settings = settings
        self.config = open_api_models.Config(
            type="access_key",
            access_key_id=settings.ACCESS_KEY_ID,
            access_key_secret=settings.ACCESS_KEY_SECRET,
            endpoint=settings.ENDPOINT
        )

        # 初始化client
        self.client = docmind_api20220711Client(self.config)
    
    async def submit_file(self, file_url: str, file_name: str) -> Dict[str, Any]:
        """
        异步提交文件到阿里云文档解析服务

        Args:
            file_url (str): 文件URL路径
            file_name (str): 文件名称，必须包含后缀

        Returns:
            Dict[str, Any]: 包含任务ID和状态的响应

        Raises:
            Exception: 当提交任务失败时抛出异常
        """

        # 创建请求参数
        print(f"提交阿里云解析任务 - 文件URL: {file_url}, 文件名: {file_name}")
        request = docmind_api20220711_models.SubmitDocParserJobRequest(
            file_url=file_url,
            file_name=file_name,
            formula_enhancement=True,
            llm_enhancement=True,
        )

        try:
            # 使用 asyncio.to_thread 将同步调用转为异步
            response = await asyncio.to_thread(self.client.submit_doc_parser_job, request)

            print(f"阿里云提交响应: {response}")
            print(f"响应body: {response.body if response else None}")
            print(f"响应data: {response.body.data if response and response.body else None}")

            # 解析响应
            if response and response.body and response.body.data:
                job_id = response.body.data.id
                print(f"✅ 阿里云任务提交成功，任务ID: {job_id}")
                return {
                    "success": True,
                    "job_id": job_id,
                    "message": "文档解析任务提交成功"
                }
            else:
                raise Exception("阿里云服务返回空响应或数据为空")

        except Exception as e:
            error_msg = str(e)
            print(f"阿里云文档解析提交失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "文档解析任务提交失败"
            }

    async def get_job_result(self, job_id: str) -> Dict[str, Any]:
        """
        获取文档解析任务结果（仅在任务成功完成后调用）

        Args:
            job_id (str): 任务ID

        Returns:
            Dict[str, Any]: 任务结果
        """
        # 根据参考文档，需要指定layout_num和layout_step_size参数
        request = docmind_api20220711_models.GetDocParserResultRequest(
            id=job_id,
            layout_num=0,  # 从第0个layout开始
            layout_step_size=3000  # 最大步长，获取所有内容
        )

        try:
            response = await asyncio.to_thread(self.client.get_doc_parser_result, request)

            # 详细的响应调试信息
            print(f"阿里云结果获取调试 - job_id: {job_id}")
            print(f"response: {response}")
            print(f"response.body: {response.body if response else None}")

            # 检查响应是否有效
            if not response:
                raise Exception("阿里云结果获取返回None响应")

            if not response.body:
                raise Exception("阿里云结果获取响应body为None")

            if not response.body.data:
                raise Exception("阿里云结果获取响应data为None")

            print(f"response.body.data类型: {type(response.body.data)}")

            # 检查response.body.data是否存在
            if hasattr(response.body, 'data'):
                result_data = response.body.data
                print(f"result_data类型: {type(result_data)}")

                # 检查是否是字典并包含layouts键
                if isinstance(result_data, dict) and 'layouts' in result_data:
                    layouts = result_data['layouts']
                    print(f"从字典中获取layouts，找到 {len(layouts)} 个layout")

                    # 将layouts转换为markdown内容
                    markdown_content = ""
                    for layout in layouts:
                        # 检查layout是否是字典并包含markdownContent键
                        if isinstance(layout, dict) and 'markdownContent' in layout and layout['markdownContent']:
                            markdown_content += layout['markdownContent'] + "\n"
                        elif isinstance(layout, dict) and 'markdown_content' in layout and layout['markdown_content']:
                            # 备用字段名
                            markdown_content += layout['markdown_content'] + "\n"
                        elif isinstance(layout, dict) and 'text' in layout and layout['text']:
                            # 如果没有markdown内容，使用纯文本
                            markdown_content += layout['text'] + "\n"
                        # 如果layout是对象而不是字典，尝试属性访问
                        elif hasattr(layout, 'markdownContent') and layout.markdownContent:
                            markdown_content += layout.markdownContent + "\n"
                        elif hasattr(layout, 'markdown_content') and layout.markdown_content:
                            markdown_content += layout.markdown_content + "\n"
                        elif hasattr(layout, 'text') and layout.text:
                            markdown_content += layout.text + "\n"

                    print(f"解析结果获取成功 - markdown长度: {len(markdown_content)}")

                    return {
                        "success": True,
                        "result": markdown_content,
                        "raw_data": result_data,
                        "message": "获取任务结果成功"
                    }
                # 如果result_data是对象而不是字典，尝试属性访问
                elif hasattr(result_data, 'layouts'):
                    layouts = result_data.layouts
                    print(f"从对象属性获取layouts，找到 {len(layouts)} 个layout")

                    # 将layouts转换为markdown内容
                    markdown_content = ""
                    for layout in layouts:
                        # 检查layout是否有markdownContent属性
                        if hasattr(layout, 'markdownContent') and layout.markdownContent:
                            markdown_content += layout.markdownContent + "\n"
                        elif hasattr(layout, 'markdown_content') and layout.markdown_content:
                            markdown_content += layout.markdown_content + "\n"
                        elif hasattr(layout, 'text') and layout.text:
                            markdown_content += layout.text + "\n"

                    print(f"解析结果获取成功 - markdown长度: {len(markdown_content)}")

                    return {
                        "success": True,
                        "result": markdown_content,
                        "raw_data": result_data,
                        "message": "获取任务结果成功"
                    }
                else:
                    print(f"result_data中没有找到layouts")
                    if isinstance(result_data, dict):
                        print(f"result_data字典键: {list(result_data.keys())}")
                    else:
                        print(f"result_data对象属性: {dir(result_data)}")
                    return {
                        "success": False,
                        "error": "解析结果中没有layouts",
                        "message": "任务结果格式错误"
                    }
            else:
                print(f"response.body没有data属性")
                return {
                    "success": False,
                    "error": "响应中没有data字段",
                    "message": "任务结果格式错误"
                }

        except Exception as e:
            error_msg = str(e)
            print(f"获取阿里云任务结果失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "获取任务结果失败"
            }

    async def query_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        查询文档解析任务状态（仅查询状态，不获取结果）

        Args:
            job_id (str): 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """
        request = docmind_api20220711_models.QueryDocParserStatusRequest(id=job_id)

        try:
            response = await asyncio.to_thread(self.client.query_doc_parser_status, request)

            print(f"状态查询响应调试 - job_id: {job_id}")
            print(f"response: {response}")
            print(f"response.body: {response.body if response else None}")

            # 检查响应是否有效
            if not response:
                raise Exception("阿里云状态查询返回None响应")

            if not response.body:
                raise Exception("阿里云状态查询响应body为None")

            if not response.body.data:
                raise Exception("阿里云状态查询响应data为None")

            print(f"response.body.data: {response.body.data}")

            # 检查status字段是否存在
            if not hasattr(response.body.data, 'status'):
                raise Exception("阿里云状态查询响应data中没有status字段")

            status = response.body.data.status
            print(f"任务 {job_id} 状态: {status}")

            return {
                "success": True,
                "status": status,
                "message": f"任务状态: {status}"
            }

        except Exception as e:
            error_msg = str(e)
            print(f"查询阿里云任务状态失败: {error_msg}")
            return {
                "success": False,
                "error": error_msg,
                "message": "查询任务状态失败"
            }

    async def check_job_status(self, job_id: str) -> Dict[str, Any]:
        """
        检查文档解析任务状态

        Args:
            job_id (str): 任务ID

        Returns:
            Dict[str, Any]: 任务状态信息
        """
        # 先查询状态
        status_result = await self.query_job_status(job_id)

        if status_result["success"]:
            status = status_result.get("status", "unknown")

            # 阿里云文档解析的状态映射
            # init: 任务排队中
            # processing: 处理中
            # success: 成功
            # fail: 失败
            is_completed = status in ["success", "fail"]
            is_success = status == "success"

            print(f"任务 {job_id} 状态检查: {status}, 已完成: {is_completed}, 成功: {is_success}")

            return {
                "success": True,
                "status": status,
                "is_completed": is_completed,
                "is_success": is_success,
                "message": f"任务状态: {status}"
            }
        else:
            print(f"任务 {job_id} 状态检查失败: {status_result.get('error', 'unknown error')}")
            return status_result

    async def download_images_from_markdown(self, markdown_content: str, minio_service, original_filename: str) -> str:
        """
        从markdown内容中下载图片并替换为MinIO URL

        Args:
            markdown_content (str): 包含阿里云临时图片URL的markdown内容
            minio_service: MinIO服务实例

        Returns:
            str: 替换后的markdown内容
        """
        import re
        import httpx
        import uuid
        from urllib.parse import urlparse

        # 匹配markdown中的图片URL
        img_pattern = r'!\[([^\]]*)\]\(([^)]+)\)'

        async def replace_image_url(match):
            alt_text = match.group(1)
            img_url = match.group(2)

            try:
                # 下载图片
                async with httpx.AsyncClient() as client:
                    response = await client.get(img_url)
                    response.raise_for_status()

                    # 生成新的文件名，使用指定的路径约定
                    parsed_url = urlparse(img_url)
                    file_extension = parsed_url.path.split('.')[-1] if '.' in parsed_url.path else 'jpg'
                    # 从原始文件名中移除扩展名
                    base_filename = original_filename.rsplit('.', 1)[0] if '.' in original_filename else original_filename
                    new_filename = f"parsed_markdowns/{base_filename}_images/{uuid.uuid4()}.{file_extension}"

                    # 上传到MinIO
                    minio_url = await minio_service.upload_file(
                        bucket_name=self.settings.BUCKET_NAME or "zhanshu",
                        object_name=new_filename,
                        file_stream=response.content,
                        length=len(response.content),
                        content_type=f"image/{file_extension}"
                    )

                    return f"![{alt_text}]({minio_url})"

            except Exception as e:
                print(f"下载图片失败 {img_url}: {e}")
                # 如果下载失败，保留原URL
                return match.group(0)

        # 异步替换所有图片URL
        import asyncio
        matches = list(re.finditer(img_pattern, markdown_content))

        if not matches:
            return markdown_content

        # 并发处理所有图片
        tasks = [replace_image_url(match) for match in matches]
        replacements = await asyncio.gather(*tasks)

        # 替换内容
        result = markdown_content
        for match, replacement in zip(reversed(matches), reversed(replacements)):
            result = result[:match.start()] + replacement + result[match.end():]

        return result