#!/usr/bin/env python3
"""
测试Redis任务队列
Test Redis Task Queue
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from src.services.redis_queue_service import RedisTaskQueue
from src.config.settings import redis_settings


async def test_redis_connection():
    """测试Redis连接"""
    try:
        queue = RedisTaskQueue(redis_settings)
        
        # 测试基本连接
        stats = await queue.get_queue_stats()
        print(f"✅ Redis连接成功")
        print(f"队列统计: {stats}")
        
        # 测试推送任务
        task_id = await queue.push_task({
            "document_uuid": "test-doc-123",
            "filename": "test.pdf",
            "test": True
        })
        print(f"✅ 任务推送成功: {task_id}")
        
        # 测试获取任务
        task_data = await queue.pop_task("test-worker")
        if task_data:
            print(f"✅ 任务获取成功: {task_data['task_id']}")
            
            # 测试完成任务
            await queue.complete_task(task_data["task_id"], {"result": "test completed"})
            print(f"✅ 任务完成成功")
        else:
            print("❌ 没有获取到任务")
        
        # 最终统计
        final_stats = await queue.get_queue_stats()
        print(f"最终队列统计: {final_stats}")
        
        return True
        
    except Exception as e:
        print(f"❌ Redis测试失败: {e}")
        return False


async def main():
    """主函数"""
    print("开始测试Redis任务队列...")
    
    success = await test_redis_connection()
    
    if success:
        print("\n🎉 Redis任务队列测试通过！")
    else:
        print("\n💥 Redis任务队列测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
