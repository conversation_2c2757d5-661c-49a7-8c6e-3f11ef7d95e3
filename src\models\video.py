from tortoise.models import Model
from tortoise import fields

class Video(Model):
    id = fields.IntField(pk=True) # 视频ID
    title = fields.CharField(max_length=255) # 视频标题
    description = fields.TextField() # 视频描述
    video_url = fields.CharField(max_length=500) # 视频地址
    categories = fields.JSONField(default = list) # 视频分类 使用字典存储, 例如{"类别1": ["子类1", "子类2"], "类别2": ["子类3", "子类4"]}
    tags = fields.JSONField(default = list) # 视频标签 使用字典存储, 例如{"标签1": ["子标签1", "子标签2"], "标签2": ["子标签3", "子标签4"]}
    status = fields.CharField(max_length=10, default="pending") # 视频状态, pending（待开始）, processing（处理中）, completed（已完成）, failed(失败)
    created_at = fields.DatetimeField(auto_now_add=True) # 视频创建时间
    updated_at = fields.DatetimeField(auto_now=True) # 视频更新时间
    
    class Meta:
        table = "videos"
        