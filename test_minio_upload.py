#!/usr/bin/env python3
"""
测试MinIO上传功能
Test MinIO Upload Functionality

验证移除upload_file_content方法后，upload_file方法能正常工作
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))


async def test_minio_upload():
    """测试MinIO上传功能"""
    print("🧪 测试MinIO上传功能...")
    
    try:
        from src.core.dependencies import get_minio_service
        
        # 获取MinIO服务
        minio_service = get_minio_service()
        print("✅ MinIO服务获取成功")
        
        # 准备测试数据
        test_content = b"This is a test file content for MinIO upload test."
        test_filename = "test_upload.txt"
        bucket_name = "test-bucket"
        
        print(f"📤 测试上传文件: {test_filename}")
        print(f"📦 存储桶: {bucket_name}")
        print(f"📄 文件大小: {len(test_content)} bytes")
        
        # 测试upload_file方法
        try:
            file_url = await minio_service.upload_file(
                bucket_name=bucket_name,
                object_name=f"test/{test_filename}",
                file_stream=test_content,
                length=len(test_content),
                content_type="text/plain"
            )
            
            if file_url:
                print(f"✅ 文件上传成功")
                print(f"🔗 文件URL: {file_url}")
                return True
            else:
                print("❌ 文件上传失败 - 返回空URL")
                return False
                
        except Exception as upload_error:
            print(f"❌ 文件上传异常: {upload_error}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


async def test_minio_methods():
    """测试MinIO服务方法"""
    print("\n🔍 检查MinIO服务方法...")
    
    try:
        from src.core.dependencies import get_minio_service
        
        minio_service = get_minio_service()
        
        # 检查方法是否存在
        methods_to_check = [
            'upload_file',
            'get_public_url',
            'test_connection'
        ]
        
        for method_name in methods_to_check:
            if hasattr(minio_service, method_name):
                print(f"✅ 方法存在: {method_name}")
            else:
                print(f"❌ 方法缺失: {method_name}")
                return False
        
        # 检查upload_file_content方法是否已移除
        if hasattr(minio_service, 'upload_file_content'):
            print("❌ upload_file_content方法仍然存在，应该已被移除")
            return False
        else:
            print("✅ upload_file_content方法已成功移除")
        
        return True
        
    except Exception as e:
        print(f"❌ 方法检查失败: {e}")
        return False


async def test_connection():
    """测试MinIO连接"""
    print("\n🔗 测试MinIO连接...")
    
    try:
        from src.core.dependencies import get_minio_service
        
        minio_service = get_minio_service()
        
        # 测试连接
        connection_ok = await minio_service.test_connection()
        
        if connection_ok:
            print("✅ MinIO连接测试成功")
            return True
        else:
            print("❌ MinIO连接测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试异常: {e}")
        return False


async def main():
    """主测试函数"""
    print("🧪 开始MinIO上传功能测试")
    print("=" * 60)
    
    tests = [
        ("MinIO方法检查", test_minio_methods),
        ("MinIO连接测试", test_connection),
        ("MinIO上传测试", test_minio_upload),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 执行测试: {test_name}")
            result = await test_func()
            if result:
                passed += 1
                print(f"✅ {test_name} - 通过")
            else:
                print(f"❌ {test_name} - 失败")
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MinIO上传功能正常。")
        print("\n📋 验证结果:")
        print("  ✅ upload_file_content方法已成功移除")
        print("  ✅ upload_file方法工作正常")
        print("  ✅ API接口适配完成")
        return 0
    else:
        print("⚠️  部分测试失败，请检查MinIO配置。")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
