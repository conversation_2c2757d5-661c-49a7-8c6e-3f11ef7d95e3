"""
文档上传和处理API
Document Upload and Processing API

实现文档上传、解析、向量化的完整流程API接口
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, File, UploadFile, HTTPException, status, Form
from pydantic import BaseModel
import uuid
import asyncio
import json
from pathlib import Path

# 导入模型
from src.models.document import Document, DocumentBatch, TaskStatus, BatchStatus, AliTaskStatus, DifyIndexingStatus

# 导入依赖注入
from src.core.dependencies import (
    MinioServiceDep,
    DifyServiceDep,
    AlibabaCloudServiceDep,
    VideoAPISettingsDep,
    DocumentProcessorDep,
    AppSettingsDep,
    RedisQueueServiceDep
)

# 导入配置
from src.config.settings import file_extensions_settings


# ===== 请求和响应模型 =====

class DocumentUploadResponse(BaseModel):
    """文档上传响应模型"""
    success: bool
    message: str
    batch_id: Optional[str] = None
    total_files: Optional[int] = None
    uploaded_files: List[Dict[str, Any]] = []


class DocumentStatusResponse(BaseModel):
    """文档状态响应模型"""
    task_id: str
    filename: str
    status: str
    error_message: Optional[str] = None
    created_at: Optional[str] = None
    updated_at: Optional[str] = None


class BatchStatusResponse(BaseModel):
    """批次状态响应模型"""
    batch_id: str
    batch_status: str
    total_files: int
    completed_files: int
    failed_files: int
    files: List[DocumentStatusResponse] = []


class DocumentRetryRequest(BaseModel):
    """文档重试请求模型"""
    task_id: str
    reset_status: bool = True


# ===== API路由 =====

document_rag_router = APIRouter(prefix="/api/v1/documents", tags=["documents"])


@document_rag_router.post("/upload", response_model=DocumentUploadResponse)
async def upload_documents(
    files: List[UploadFile] = File(...),
    batch_name: Optional[str] = Form(None),
    minio_service: MinioServiceDep = None,
    video_settings: VideoAPISettingsDep = None,
    app_settings: AppSettingsDep = None,
    redis_queue: RedisQueueServiceDep = None
):
    """
    多文件上传接口

    支持：
    - 多文件同时上传
    - 重复文件处理（直接更新数据库记录）
    - 即时响应（不等待后续处理）
    """

    if not files:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="至少需要上传一个文件"
        )

    # 验证文件数量限制
    if len(files) > app_settings.MAX_DOCUMENT_FILES:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"文件数量超过限制，最多允许上传 {app_settings.MAX_DOCUMENT_FILES} 个文件"
        )

    # 验证文件类型
    allowed_extensions = file_extensions_settings.SUPPORTED_DOCUMENT_EXTENSIONS
    uploaded_files = []
    invalid_files = []

    for file in files:
        if not file.filename:
            invalid_files.append({"filename": "unknown", "error": "文件名为空"})
            continue

        file_extension = Path(file.filename).suffix.lower().lstrip('.')
        if file_extension not in allowed_extensions:
            invalid_files.append({
                "filename": file.filename,
                "error": f"不支持的文件类型: {file_extension}"
            })
            continue

        uploaded_files.append(file)

    if invalid_files:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={
                "message": "部分文件类型不支持",
                "invalid_files": invalid_files,
                "supported_extensions": list(allowed_extensions)
            }
        )

    try:
        # 创建批次记录
        batch = await DocumentBatch.create(
            batch_name=batch_name or f"批次上传_{uuid.uuid4().hex[:8]}",
            total_files=len(uploaded_files),
            batch_status=BatchStatus.UPLOADING
        )

        successful_uploads = []
        failed_uploads = []

        # 处理每个文件
        for file in uploaded_files:
            try:
                # 检查是否存在同名文件
                existing_doc = await Document.filter(
                    original_filename=file.filename
                ).first()

                if existing_doc:
                    # 重复文件处理：重置状态
                    await existing_doc.update_from_dict({
                        "task_status": TaskStatus.WATTING,
                        "ali_task_id": None,
                        "ali_task_status": AliTaskStatus.PENDING,
                        "dify_document_id": None,
                        "dify_batch_id": None,
                        "dify_indexing_status": DifyIndexingStatus.QUEUING,
                        "error_message": None,
                        "batch_id": batch.id,
                        "file_size": file.size or 0,
                        "content_type": file.content_type
                    })
                    await existing_doc.save()

                    # 重新上传文件到MinIO（覆盖）
                    file_content = await file.read()
                    minio_url = await minio_service.upload_file(
                        bucket_name=video_settings.BUCKET_NAME,
                        object_name=f"documents/{file.filename}",
                        file_stream=file_content,
                        length=len(file_content),
                        content_type=file.content_type or "application/octet-stream"
                    )

                    # 更新MinIO路径
                    existing_doc.minio_path = minio_url
                    await existing_doc.save()

                    successful_uploads.append({
                        "task_id": str(existing_doc.uuid),
                        "filename": file.filename,
                        "status": "updated",
                        "message": "文件已更新，重新开始处理"
                    })

                    # 推送任务到Redis队列
                    try:
                        task_id = await redis_queue.push_task({
                            "document_uuid": str(existing_doc.uuid),
                            "filename": file.filename,
                            "batch_id": str(batch.batch_uuid)
                        })
                        print(f"任务已推送到Redis队列: {task_id} - 文档: {file.filename}")
                    except Exception as e:
                        print(f"推送任务到Redis队列失败: {e}")
                        # 不影响上传流程，只记录错误

                else:
                    # 新文件处理
                    file_content = await file.read()

                    # 上传到MinIO
                    minio_url = await minio_service.upload_file(
                        bucket_name=video_settings.BUCKET_NAME,
                        object_name=f"documents/{file.filename}",
                        file_stream=file_content,
                        length=len(file_content),
                        content_type=file.content_type or "application/octet-stream"
                    )

                    # 创建文档记录
                    document = await Document.create(
                        original_filename=file.filename,
                        file_size=file.size or 0,
                        minio_path=minio_url,
                        task_status=TaskStatus.WATTING,
                        batch_id=batch.id,
                        content_type=file.content_type
                    )

                    successful_uploads.append({
                        "task_id": str(document.uuid),
                        "filename": file.filename,
                        "status": "uploaded",
                        "message": "文件上传成功，等待处理"
                    })

                    # 推送任务到Redis队列
                    try:
                        task_id = await redis_queue.push_task({
                            "document_uuid": str(document.uuid),
                            "filename": file.filename,
                            "batch_id": str(batch.batch_uuid)
                        })
                        print(f"任务已推送到Redis队列: {task_id} - 文档: {file.filename}")
                    except Exception as e:
                        print(f"推送任务到Redis队列失败: {e}")
                        # 不影响上传流程，只记录错误

            except Exception as e:
                failed_uploads.append({
                    "filename": file.filename,
                    "error": str(e)
                })

        # 更新批次状态
        await batch.update_from_dict({
            "uploaded_files": len(successful_uploads),
            "failed_files": len(failed_uploads),
            "batch_status": BatchStatus.PROCESSING if successful_uploads else BatchStatus.FAILED
        })
        await batch.save()

        # 文档已推送到Redis任务队列进行处理

        return DocumentUploadResponse(
            success=True,
            message=f"批次上传完成，成功: {len(successful_uploads)}, 失败: {len(failed_uploads)}",
            batch_id=str(batch.batch_uuid),
            total_files=len(uploaded_files),
            uploaded_files=successful_uploads
        )

    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传处理失败: {str(e)}"
        )


@document_rag_router.get("/batch/{batch_id}/status", response_model=BatchStatusResponse)
async def get_batch_status(batch_id: str):
    """
    查询批次状态

    Args:
        batch_id: 批次UUID
    """

    try:
        # 查找批次
        batch = await DocumentBatch.filter(batch_uuid=batch_id).first()
        if not batch:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="批次不存在"
            )

        # 查找批次下的所有文档
        documents = await Document.filter(batch_id=batch.id).all()

        # 构建文档状态列表
        files_status = []
        for doc in documents:
            files_status.append(DocumentStatusResponse(
                task_id=str(doc.uuid),
                filename=doc.original_filename,
                status=doc.task_status.value,
                error_message=doc.error_message,
                created_at=doc.created_at.isoformat() if doc.created_at else None,
                updated_at=doc.updated_at.isoformat() if doc.updated_at else None
            ))

        return BatchStatusResponse(
            batch_id=batch_id,
            batch_status=batch.batch_status.value,
            total_files=batch.total_files,
            completed_files=batch.completed_files,
            failed_files=batch.failed_files,
            files=files_status
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询批次状态失败: {str(e)}"
        )


@document_rag_router.get("/{task_id}/status", response_model=DocumentStatusResponse)
async def get_document_status(task_id: str):
    """
    查询单个文档状态

    Args:
        task_id: 文档UUID
    """

    try:
        document = await Document.filter(uuid=task_id).first()
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        return DocumentStatusResponse(
            task_id=task_id,
            filename=document.original_filename,
            status=document.task_status.value,
            error_message=document.error_message,
            created_at=document.created_at.isoformat() if document.created_at else None,
            updated_at=document.updated_at.isoformat() if document.updated_at else None
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询文档状态失败: {str(e)}"
        )


@document_rag_router.post("/{task_id}/retry")
async def retry_document_processing(
    task_id: str,
    request: DocumentRetryRequest,
    redis_queue: RedisQueueServiceDep = None
):
    """
    重试文档处理

    Args:
        task_id: 文档UUID
        request: 重试请求参数
    """

    try:
        document = await Document.filter(uuid=task_id).first()
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        if request.reset_status:
            # 重置所有状态
            await document.update_from_dict({
                "task_status": TaskStatus.WATTING,
                "ali_task_id": None,
                "ali_task_status": AliTaskStatus.PENDING,
                "dify_document_id": None,
                "dify_batch_id": None,
                "dify_indexing_status": DifyIndexingStatus.QUEUING,
                "error_message": None
            })
            await document.save()

        # 重新推送到Redis队列
        try:
            batch = await document.batch
            redis_task_id = await redis_queue.push_task({
                "document_uuid": task_id,
                "filename": document.original_filename,
                "batch_id": str(batch.batch_uuid) if batch else None
            })
            print(f"重试任务已推送到Redis队列: {redis_task_id} - 文档: {document.original_filename}")
        except Exception as e:
            print(f"推送重试任务到Redis队列失败: {e}")

        return {
            "success": True,
            "message": "文档已重新加入处理队列",
            "task_id": task_id
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重试文档处理失败: {str(e)}"
        )


@document_rag_router.post("/{task_id}/process")
async def process_document_manually(
    task_id: str,
    processor: DocumentProcessorDep = None
):
    """
    手动触发文档处理

    Args:
        task_id: 文档UUID
        processor: 文档处理器实例
    """

    try:
        document = await Document.filter(uuid=task_id).first()
        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 手动触发处理
        result = await processor.process_document(task_id)

        return {
            "success": result["success"],
            "message": result.get("message", "处理完成"),
            "task_id": task_id,
            "error": result.get("error") if not result["success"] else None
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"手动处理文档失败: {str(e)}"
        )