"""
文档处理后台服务
Document Processing Background Service

实现阿里云文档解析和Dify向量化的完整流程
"""

import asyncio
import json
from typing import Dict, Any, Optional
from datetime import datetime

# 导入模型
from src.models.document import Document, DocumentBatch, TaskStatus, BatchStatus, AliTaskStatus, DifyIndexingStatus

# 导入服务
from src.core.dependencies import (
    get_minio_service,
    get_dify_service,
    get_alibaba_cloud_service
)


class DocumentProcessor:
    """文档处理器"""

    def __init__(self, minio_service=None, dify_service=None, alibaba_service=None, dify_settings=None):
        """
        初始化文档处理器

        Args:
            minio_service: MinIO服务实例，如果为None则使用依赖注入获取
            dify_service: Dify服务实例，如果为None则使用依赖注入获取
            alibaba_service: 阿里云服务实例，如果为None则使用依赖注入获取
            dify_settings: Dify配置实例，如果为None则使用依赖注入获取
        """
        # 使用传入的服务实例，如果没有则通过依赖注入获取
        self.minio_service = minio_service or get_minio_service()
        self.dify_service = dify_service or get_dify_service()
        self.alibaba_service = alibaba_service or get_alibaba_cloud_service()

        # 获取Dify配置
        if dify_settings is None:
            from src.config.settings import dify_settings as default_dify_settings
            self.dify_settings = default_dify_settings
        else:
            self.dify_settings = dify_settings
        
    async def process_document(self, document_uuid: str) -> Dict[str, Any]:
        """
        处理单个文档的完整流程
        
        Args:
            document_uuid: 文档UUID
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        
        try:
            # 获取文档记录
            document = await Document.filter(uuid=document_uuid).first()
            if not document:
                return {
                    "success": False,
                    "error": "文档不存在",
                    "document_uuid": document_uuid
                }
            
            print(f"开始处理文档: {document.original_filename}")
            
            # 阶段一：阿里云文档解析
            ali_result = await self._process_alibaba_parsing(document)
            if not ali_result["success"]:
                error_msg = f"阿里云解析失败: {ali_result['error']}"
                await self._mark_document_failed(document, error_msg)
                print(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "document_uuid": document_uuid,
                    "stage": "alibaba_parsing"
                }

            # 阶段二：Dify向量化
            dify_result = await self._process_dify_indexing(document, ali_result["markdown_content"])
            if not dify_result["success"]:
                error_msg = f"Dify向量化失败: {dify_result['error']}"
                await self._mark_document_failed(document, error_msg)
                print(f"❌ {error_msg}")
                return {
                    "success": False,
                    "error": error_msg,
                    "document_uuid": document_uuid,
                    "stage": "dify_indexing"
                }
            
            # 标记完成
            await self._mark_document_completed(document)
            
            # 更新批次状态
            if document.batch_id:
                await self._update_batch_status(document.batch_id)
            
            return {
                "success": True,
                "message": "文档处理完成",
                "document_uuid": document_uuid,
                "filename": document.original_filename
            }
            
        except Exception as e:
            error_msg = f"文档处理异常: {str(e)}"
            print(error_msg)
            
            # 尝试标记文档失败
            try:
                document = await Document.filter(uuid=document_uuid).first()
                if document:
                    await self._mark_document_failed(document, error_msg)
            except:
                pass
                
            return {
                "success": False,
                "error": error_msg,
                "document_uuid": document_uuid
            }
    
    async def _process_alibaba_parsing(self, document: Document) -> Dict[str, Any]:
        """
        处理阿里云文档解析
        
        Args:
            document: 文档实例
            
        Returns:
            Dict[str, Any]: 解析结果
        """
        
        try:
            # 更新状态为解析中
            document.task_status = TaskStatus.PROCESSING
            document.ali_task_status = AliTaskStatus.PROCESSING
            await document.save()
            
            print(f"提交阿里云解析任务: {document.original_filename}")
            
            # 提交解析任务
            submit_result = await self.alibaba_service.submit_file(
                file_url=document.minio_path,
                file_name=document.original_filename
            )
            
            if not submit_result["success"]:
                return {
                    "success": False,
                    "error": f"阿里云任务提交失败: {submit_result['error']}"
                }
            
            # 保存任务ID
            document.ali_task_id = submit_result["job_id"]
            await document.save()
            
            # 轮询等待解析完成
            max_wait_time = 300  # 5分钟超时
            check_interval = 10  # 10秒检查一次
            waited_time = 0
            
            while waited_time < max_wait_time:
                await asyncio.sleep(check_interval)
                waited_time += check_interval

                print(f"检查阿里云任务状态 - 任务ID: {document.ali_task_id}, 已等待: {waited_time}s")

                # 检查任务状态
                status_result = await self.alibaba_service.check_job_status(document.ali_task_id)

                if not status_result["success"]:
                    error_msg = f"检查阿里云任务状态失败: {status_result.get('error', 'unknown error')}"
                    print(f"❌ {error_msg}")
                    return {
                        "success": False,
                        "error": error_msg
                    }

                print(f"任务状态: {status_result.get('status', 'unknown')}, 已完成: {status_result.get('is_completed', False)}")

                if status_result["is_completed"]:
                    if status_result["is_success"]:
                        # 获取解析结果
                        print("任务成功完成，获取解析结果...")
                        result = await self.alibaba_service.get_job_result(document.ali_task_id)
                        if result["success"] and result["result"]:
                            print(f"获取到解析结果，内容长度: {len(result['result'])} 字符")
                            # 处理图片URL替换
                            markdown_content = result["result"]
                            processed_content = await self.alibaba_service.download_images_from_markdown(
                                markdown_content, self.minio_service, document.original_filename
                            )

                            # 更新状态
                            document.ali_task_status = AliTaskStatus.SUCCESS
                            await document.save()

                            return {
                                "success": True,
                                "markdown_content": processed_content
                            }
                        else:
                            error_msg = f"获取阿里云解析结果失败: {result.get('error', 'no result content')}"
                            print(f"❌ {error_msg}")
                            return {
                                "success": False,
                                "error": error_msg
                            }
                    else:
                        error_msg = f"阿里云文档解析失败，状态: {status_result.get('status', 'unknown')}"
                        print(f"❌ {error_msg}")
                        return {
                            "success": False,
                            "error": error_msg
                        }
            
            # 超时
            return {
                "success": False,
                "error": "阿里云文档解析超时"
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": f"阿里云解析异常: {str(e)}"
            }
    
    async def _process_dify_indexing(self, document: Document, markdown_content: str) -> Dict[str, Any]:
        """
        处理Dify向量化
        
        Args:
            document: 文档实例
            markdown_content: Markdown内容
            
        Returns:
            Dict[str, Any]: 向量化结果
        """
        
        try:
            # 更新状态为向量化中
            document.task_status = TaskStatus.INDEXING
            document.dify_indexing_status = DifyIndexingStatus.INDEXING
            await document.save()
            
            print(f"提交Dify向量化任务: {document.original_filename}")
            
            # 确保知识库存在
            knowledge_base_name = self.dify_settings.DIFY_DOCUMENT_KNOWLEDGE_BASE
            kb_result = await self.dify_service.ensure_knowledge_base_exists_by_name(knowledge_base_name)
            
            if not kb_result["success"]:
                return {
                    "success": False,
                    "error": f"创建Dify知识库失败: {kb_result['error']}"
                }
            
            dataset_id = kb_result["dataset_id"]
            document.dify_dataset_id = dataset_id
            await document.save()
            
            # 提交文档到Dify
            submit_result = await self.dify_service.create_document_by_text(
                dataset_id=dataset_id,
                name=document.original_filename,
                text=markdown_content
            )
            
            if not submit_result["success"]:
                return {
                    "success": False,
                    "error": f"Dify文档创建失败: {submit_result['error']}"
                }
            
            # 保存Dify相关ID
            document.dify_document_id = submit_result.get("document_id")
            document.dify_batch_id = submit_result.get("batch_id")
            await document.save()
            
            # 轮询等待向量化完成
            max_wait_time = 300  # 5分钟超时
            check_interval = 10  # 10秒检查一次
            waited_time = 0
            
            while waited_time < max_wait_time:
                await asyncio.sleep(check_interval)
                waited_time += check_interval
                
                # 检查向量化状态
                if document.dify_batch_id:
                    status_result = await self.dify_service.check_document_indexing_status(
                        dataset_id, document.dify_batch_id
                    )
                    
                    if status_result["success"]:
                        indexing_status = status_result.get("indexing_status", "processing")
                        
                        if indexing_status == "completed":
                            document.dify_indexing_status = DifyIndexingStatus.COMPLETED
                            await document.save()
                            return {"success": True}
                        elif indexing_status == "error":
                            return {
                                "success": False,
                                "error": "Dify向量化失败"
                            }
            
            # 超时，但不一定失败，可能还在处理中
            return {"success": True}  # 暂时标记为成功，让用户可以查看状态
            
        except Exception as e:
            return {
                "success": False,
                "error": f"Dify向量化异常: {str(e)}"
            }
    
    async def _mark_document_failed(self, document: Document, error_message: str):
        """
        标记文档处理失败

        Args:
            document: 文档实例
            error_message: 错误信息
        """
        try:
            # 更新文档状态
            document.task_status = TaskStatus.FAILED
            document.error_message = error_message[:2000]  # 限制错误信息长度

            # 根据当前状态设置具体的失败状态
            if document.ali_task_status == AliTaskStatus.PROCESSING:
                document.ali_task_status = AliTaskStatus.FAILED
            elif document.dify_indexing_status == DifyIndexingStatus.INDEXING:
                document.dify_indexing_status = DifyIndexingStatus.ERROR

            await document.save()

            print(f"❌ 文档处理失败: {document.original_filename} - {error_message}")

            # 更新批次状态
            if document.batch_id:
                await self._update_batch_status(document.batch_id)

        except Exception as e:
            print(f"标记文档失败时出错: {e}")
    
    async def _mark_document_completed(self, document: Document):
        """
        标记文档处理完成

        Args:
            document: 文档实例
        """
        try:
            document.task_status = TaskStatus.COMPLETED
            document.ali_task_status = AliTaskStatus.SUCCESS
            document.dify_indexing_status = DifyIndexingStatus.COMPLETED
            document.error_message = None
            await document.save()

            print(f"✅ 文档处理完成: {document.original_filename}")

        except Exception as e:
            print(f"标记文档完成时出错: {e}")
    
    async def _update_batch_status(self, batch_id: int):
        """更新批次状态"""
        try:
            batch = await DocumentBatch.get(id=batch_id)
            documents = await Document.filter(batch_id=batch_id).all()
            
            total_files = len(documents)
            completed_files = len([d for d in documents if d.task_status == TaskStatus.COMPLETED])
            failed_files = len([d for d in documents if d.task_status == TaskStatus.FAILED])
            processing_files = total_files - completed_files - failed_files
            
            # 更新统计
            batch.completed_files = completed_files
            batch.failed_files = failed_files
            
            # 更新批次状态
            if processing_files == 0:
                if failed_files == 0:
                    batch.batch_status = BatchStatus.COMPLETED
                elif failed_files == total_files:
                    batch.batch_status = BatchStatus.FAILED
                else:
                    batch.batch_status = BatchStatus.PARTIAL_FAILED
            else:
                batch.batch_status = BatchStatus.PROCESSING
            
            await batch.save()
            
        except Exception as e:
            print(f"更新批次状态失败: {e}")


# ===== Redis任务处理器 =====

async def process_document_task(task_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    处理文档任务的函数，用于Redis任务队列

    Args:
        task_data: 任务数据，包含document_uuid等信息

    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        document_uuid = task_data.get("document_uuid")
        if not document_uuid:
            return {
                "success": False,
                "error": "缺少document_uuid参数"
            }

        # 创建文档处理器实例
        from src.core.dependencies import get_document_processor
        processor = get_document_processor()

        # 处理文档
        result = await processor.process_document(document_uuid)

        return result

    except Exception as e:
        return {
            "success": False,
            "error": f"任务处理异常: {str(e)}"
        }
