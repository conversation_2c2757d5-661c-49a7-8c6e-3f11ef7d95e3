from tortoise.models import Model
from tortoise import fields
from enum import Enum
import uuid

class TaskStatus(str, Enum):
    WATTING = "waiting" # 等待
    PROCESSING = "processing" #解析
    INDEXING = "indexing" # 向量化
    COMPLETED = "completed" # 完成
    FAILED = "failed" # 失败
    
class AliTaskStatus(str, Enum):
    PENDING="pending" # 等待
    PROCESSING="processing" #解析
    SUCCESS="success" # 完成
    FAILED="failed" # 失败
    
class DifyIndexingStatus(str, Enum):
    """定义了Dify向量化这一个步骤的内部状态"""
    QUEUING = "queuing"       # 排队中：任务已提交给Dify，等待Dify调度
    INDEXING = "indexing"     # 索引中：Dify正在处理
    COMPLETED = "completed"   # 完成
    ERROR = "error"           # 出错
    
# 增加批次管理
class BatchStatus(str, Enum):
    UPLOADING = "uploading" # 上传中
    PROCESSING = "processing" # 解析中
    COMPLETED = "completed" # 完成
    PARTIAL_FAILED = "partial_failed" # 部分失败
    FAILED = "failed" # 失败
    
    
class DocumentBatch(Model):
    """文档批次表"""
    id = fields.IntField(pk=True) # 批次ID
    batch_uuid = fields.UUIDField(default=uuid.uuid4, unique=True) # 批次UUID
    batch_name = fields.CharField(max_length=255,null=True) # 批次名称
    total_files = fields.IntField(default=0) # 批次总文件数
    uploaded_files = fields.IntField(default=0) # 批次已上传文件数
    completed_files = fields.IntField(default=0) # 批次已完成文件数
    failed_files = fields.IntField(default=0) # 批次已失败文件数
    batch_status = fields.CharEnumField(BatchStatus, default=BatchStatus.UPLOADING) # 批次状态
    error_message = fields.TextField(null=True) # 错误信息
    created_at = fields.DatetimeField(auto_now_add=True) # 批次创建时间
    updated_at = fields.DatetimeField(auto_now=True) # 批次更新时间
    
    class Meta:
        table = "document_batches"
    
   
   
    

class Document(Model):
    id = fields.IntField(pk=True) # 文件ID
    uuid = fields.UUIDField(default=uuid.uuid4, unique=True) # 文件UUID
    original_filename = fields.CharField(max_length=255, not_null=True) # 文件原始名称
    file_size = fields.BigIntField(not_null=True) # 文件大小
    minio_path= fields.CharField(max_length=255,not_null=True) # 文件在minio中的路径
    task_status = fields.CharEnumField(TaskStatus, default=TaskStatus.WATTING) # 文件任务状态
    
    # 阿里云文档解析
    ali_task_id = fields.CharField(max_length=255, null=True) # 阿里云文档解析任务ID
    ali_task_status = fields.CharEnumField(AliTaskStatus, default=AliTaskStatus.PENDING) # 阿里云文档解析任务状态
    
    
    # dify向量化
    dify_dataset_id = fields.CharField(max_length=255, null=True) # dify向量化数据集ID
    dify_document_id = fields.CharField(max_length=255, null=True) # dify向量化文档ID
    dify_batch_id = fields.CharField(max_length=255, null=True) # dify向量化批次ID
    dify_indexing_status = fields.CharEnumField(DifyIndexingStatus, default=DifyIndexingStatus.QUEUING) # dify向量化状态
    
    error_message = fields.TextField(null=True) # 错误信息
    created_at = fields.DatetimeField(auto_now_add=True) # 文件创建时间
    updated_at = fields.DatetimeField(auto_now=True) # 文件更新时间
    
    batch = fields.ForeignKeyField("models.DocumentBatch", related_name="documents", null=True) #管理批次表
    content_type = fields.CharField(max_length=255, null=True) # 文件类型
    
    class Meta:
        table = "documents"