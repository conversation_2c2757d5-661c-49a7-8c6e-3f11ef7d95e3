from minio import Minio
from minio.error import S3Error
import io
import json
import time
import asyncio
from typing import Optional
from src.config.settings import MinIOSettings

class MinioService:
    DEFAULT_FOLDERS = ["videos/", "docs/", "parsed_markdowns/"]

    def __init__(self, settings: MinIOSettings):
        """
        初始化minio客户端

        Args:
            settings: MinIO配置实例
        """
        self.settings = settings
        self.endpoint = settings.MINIO_ENDPOINT
        self.access_key = settings.MINIO_ACCESS_KEY
        self.secret_key = settings.MINIO_SECRET_KEY
        self.secure = settings.MINIO_SECURE

        # MinIO 客户端不直接支持 timeout 和 retry 参数
        # 我们将在应用层实现重试和超时控制
        self.client = Minio(
            self.endpoint,
            access_key=self.access_key,
            secret_key=self.secret_key,
            secure=self.secure
        )

        # # 配置 HTTP 客户端的超时（如果支持）
        # try:
        #     # 尝试设置底层 HTTP 客户端的超时
        #     if hasattr(self.client._http, 'timeout'):
        #         self.client._http.timeout = self.settings.MINIO_WRITE_TIMEOUT
        # except Exception as e:
        #     print(f"⚠️  无法设置 HTTP 客户端超时: {e}")
        #     print("   将使用应用层超时控制")

        print(f"MinIO 客户端初始化完成: {self.endpoint}")
        print(f"超时配置: 连接={self.settings.MINIO_CONNECT_TIMEOUT}s, 读取={self.settings.MINIO_READ_TIMEOUT}s, 写入={self.settings.MINIO_WRITE_TIMEOUT}s")
        print(f"重试配置: 最大重试={self.settings.MINIO_MAX_RETRIES}次, 延迟={self.settings.MINIO_RETRY_DELAY}s")

    def _set_public_read_policy(self, bucket_name: str):
        """设置存储桶的公共读取权限"""
        try:
            policy = {
                "Version": "2012-10-17",
                "Statement": [
                    {
                        "Effect": "Allow",
                        "Principal": {"AWS": "*"},
                        "Action": ["s3:GetObject", "s3:GetBucketLocation"],
                        "Resource": f"arn:aws:s3:::{bucket_name}"
                    },
                    {
                        "Effect": "Allow",
                        "Principal": {"AWS": "*"},
                        "Action": ["s3:GetObject"],
                        "Resource": f"arn:aws:s3:::{bucket_name}/*"
                    }
                ]
            }
            self.client.set_bucket_policy(bucket_name, json.dumps(policy))
            print(f"✅ 设置存储桶 '{bucket_name}' 公共读策略成功")
        except S3Error as e:
            print(f"❌ 设置存储桶 '{bucket_name}' 公共读策略失败: {e}")
            raise

    async def _set_public_read_policy_async(self, bucket_name: str):
        """
        异步设置存储桶的公共读取权限
        使用asyncio.to_thread将同步操作转换为异步操作
        """
        await asyncio.to_thread(
            self._set_public_read_policy,
            bucket_name
        )

    async def _create_default_folder(self, bucket_name: str):
        """在存储桶中创建默认文件夹结构"""
        try:
            for folder in self.DEFAULT_FOLDERS:
                data = io.BytesIO(b"")
                await asyncio.to_thread(
                    self.client.put_object,
                    bucket_name,
                    folder,
                    data,
                    0,
                    content_type="application/x-directory"
                )
            print("✅ 创建默认文件夹结构成功")
        except S3Error as e:
            print(f"❌ 创建默认文件夹结构失败: {e}")
            raise

    async def upload_file(self, bucket_name: str, object_name: str, file_stream: bytes, length: int, content_type: str = "application/octet-stream") -> Optional[str]:
        """
        异步上传文件到MinIO

        Args:
            bucket_name (str): 存储桶名称
            object_name (str): 对象名称（文件在存储桶中的路径）
            file_stream (bytes): 文件字节流
            length (int): 文件长度
            content_type (str, optional): 文件类型. Defaults to "application/octet-stream".

        Returns:
            Optional[str]: 如果上传成功，返回文件的公共访问URL；如果失败，返回None
        """
        max_retries = self.settings.MINIO_MAX_RETRIES
        retry_delay = self.settings.MINIO_RETRY_DELAY

        for attempt in range(max_retries):
            try:
                # 检查存储桶是否存在，不存在则创建
                if not await self._check_bucket_exists(bucket_name):
                    await self._create_bucket_with_policy(bucket_name)

                # 准备文件流
                file_stream_io = io.BytesIO(file_stream)

                # 执行上传
                await asyncio.to_thread(
                    self._upload_object_sync,
                    bucket_name,
                    object_name,
                    file_stream_io,
                    length,
                    content_type
                )

                print(f"✅ '{object_name}' 上传成功到存储桶 '{bucket_name}'")
                return self.get_public_url(bucket_name, object_name)

            except Exception as e:
                print(f"❌ 尝试 {attempt + 1}/{max_retries} 上传失败: {str(e)}")
                if attempt < max_retries - 1:  # 如果不是最后一次尝试
                    await asyncio.sleep(retry_delay)  # 等待后重试
                else:
                    print(f"❌ 上传失败，已达到最大重试次数: {str(e)}")
                    raise

    def _upload_object_sync(self, bucket_name: str, object_name: str, file_stream_io: io.BytesIO, length: int, content_type: str):
        """同步上传对象的辅助方法"""
        self.client.put_object(
            bucket_name,
            object_name,
            file_stream_io,
            length,
            content_type=content_type
        )

    async def _check_bucket_exists(self, bucket_name: str) -> bool:
        """检查存储桶是否存在"""
        try:
            return await asyncio.to_thread(self.client.bucket_exists, bucket_name)
        except S3Error as e:
            print(f"❌ 检查存储桶是否存在时发生错误: {e}")
            raise

    async def _create_bucket_with_policy(self, bucket_name: str):
        """异步创建存储桶并设置策略"""
        try:
            print(f"创建存储桶: {bucket_name}")
            await asyncio.get_event_loop().run_in_executor(
                None,
                self.client.make_bucket,
                bucket_name
            )

            # 设置公共读策略
            await self._set_public_read_policy_async(bucket_name)

            # 创建默认文件夹
            await self._create_default_folder(bucket_name)

        except Exception as e:
            print(f"创建存储桶失败: {e}")
            raise

    def get_public_url(self, bucket_name: str, object_name: str) -> str:
        """
        根据 MinIO 配置和对象信息构建可以访问的公开URL

        Args:
            bucket_name (str): 存储桶名称
            object_name (str): 对象名称

        Returns:
            str: 公开URL
        """
        # 根据是否使用TLS连接和对象名称构建公开URL
        protocol = "https" if self.secure else "http"
        url = f"{protocol}://{self.endpoint}/{bucket_name}/{object_name}"
        return url



    async def test_connection(self) -> bool:
        """测试 MinIO 连接"""
        try:
            # 尝试列出存储桶来测试连接
            await asyncio.get_event_loop().run_in_executor(
                None,
                list,
                self.client.list_buckets()
            )
            print("✅ MinIO 连接测试成功")
            return True
        except Exception as e:
            print(f"❌ MinIO 连接测试失败: {e}")
            return False
                 
# 注意：全局实例和工厂函数已移动到 src/core/dependencies.py
# 这里保留用于向后兼容，但建议使用依赖注入

if __name__ == "__main__":
    from src.config.settings import minio_settings
    minio_service = MinioService(minio_settings)

    # 创建默认库
    async def main():
        await minio_service._create_bucket_with_policy("zhanshu")

    asyncio.run(main())

