#!/usr/bin/env python3
"""
测试清理缓存后的行为
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_after_clear():
    """测试清理缓存后的行为"""
    print("🔍 测试清理缓存后的行为...")
    
    from src.core.dependencies import clear_service_cache, get_document_processor, get_document_task_queue
    
    # 清理缓存
    print("清理缓存...")
    clear_service_cache()
    
    # 重新获取实例
    print("重新获取实例...")
    processor = get_document_processor()
    task_queue = get_document_task_queue()
    
    print(f"处理器实例ID: {id(processor)}")
    print(f"任务队列实例ID: {id(task_queue)}")
    print(f"任务队列处理器ID: {id(task_queue.processor)}")
    print(f"处理器相同: {task_queue.processor is processor}")

if __name__ == "__main__":
    test_after_clear()
